/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.integration.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import static io.restassured.RestAssured.given;

/**
 * Helper class for handling authentication in integration tests.
 * Provides methods to obtain access tokens from the identity service.
 */
@ApplicationScoped
@Slf4j
public class AuthenticationHelper {

    @Inject
    ObjectMapper objectMapper;

    @ConfigProperty(name = "test.identity.server.url", defaultValue = "https://identity-stage.glideyoke.com")
    String identityServerUrl;

    @ConfigProperty(name = "test.identity.client.id", defaultValue = "sp-services")
    String clientId;

    @ConfigProperty(name = "test.identity.realm", defaultValue = "ui-qa-stage")
    String realm;

    @ConfigProperty(name = "test.identity.username", defaultValue = "superadmin")
    String username;

    @ConfigProperty(name = "test.identity.password", defaultValue = "UXWnVXxQrLbYBsKNxNmz")
    String password;

    private String cachedAccessToken;
    private long tokenExpiryTime;

    /**
     * Gets an access token from the identity service.
     * Caches the token and reuses it if still valid.
     *
     * @return the access token
     */
    public String getAccessToken() {
        if (isTokenValid()) {
            return cachedAccessToken;
        }

        return refreshAccessToken();
    }

    /**
     * Forces a refresh of the access token.
     *
     * @return the new access token
     */
    public String refreshAccessToken() {
        try {
            String tokenUrl = String.format("%s/auth/realms/%s/protocol/openid-connect/token", 
                    identityServerUrl, realm);

            log.info("Requesting access token from: {}", tokenUrl);

            Response response = given()
                    .contentType(ContentType.URLENC)
                    .formParam("client_id", clientId)
                    .formParam("grant_type", "password")
                    .formParam("username", username)
                    .formParam("password", password)
                    .when()
                    .post(tokenUrl)
                    .then()
                    .statusCode(200)
                    .extract()
                    .response();

            JsonNode tokenResponse = objectMapper.readTree(response.asString());
            cachedAccessToken = tokenResponse.get("access_token").asText();
            
            // Calculate expiry time (subtract 60 seconds for safety margin)
            int expiresIn = tokenResponse.get("expires_in").asInt();
            tokenExpiryTime = System.currentTimeMillis() + ((expiresIn - 60) * 1000L);

            log.info("Successfully obtained access token, expires in {} seconds", expiresIn);
            return cachedAccessToken;

        } catch (Exception e) {
            log.error("Failed to obtain access token", e);
            throw new RuntimeException("Failed to authenticate with identity service", e);
        }
    }

    /**
     * Gets an access token for a specific user.
     *
     * @param username the username
     * @param password the password
     * @return the access token
     */
    public String getAccessTokenForUser(String username, String password) {
        try {
            String tokenUrl = String.format("%s/auth/realms/%s/protocol/openid-connect/token", 
                    identityServerUrl, realm);

            Response response = given()
                    .contentType(ContentType.URLENC)
                    .formParam("client_id", clientId)
                    .formParam("grant_type", "password")
                    .formParam("username", username)
                    .formParam("password", password)
                    .when()
                    .post(tokenUrl)
                    .then()
                    .statusCode(200)
                    .extract()
                    .response();

            JsonNode tokenResponse = objectMapper.readTree(response.asString());
            return tokenResponse.get("access_token").asText();

        } catch (Exception e) {
            log.error("Failed to obtain access token for user: {}", username, e);
            throw new RuntimeException("Failed to authenticate user: " + username, e);
        }
    }

    /**
     * Checks if the current cached token is still valid.
     *
     * @return true if token is valid, false otherwise
     */
    private boolean isTokenValid() {
        return cachedAccessToken != null && System.currentTimeMillis() < tokenExpiryTime;
    }

    /**
     * Clears the cached token, forcing a refresh on next request.
     */
    public void clearToken() {
        cachedAccessToken = null;
        tokenExpiryTime = 0;
    }
}
