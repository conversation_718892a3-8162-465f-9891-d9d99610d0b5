# Test configuration for integration tests
# Override main application configuration for testing

# Test-specific identity service configuration
test:
  identity:
    server:
      url: https://identity-stage.glideyoke.com
    client:
      id: sp-services
    realm: ui-qa-stage
    username: superadmin
    password: UXWnVXxQrLbYBsKNxNmz
  company:
    id: test-company-id

# Quarkus test configuration
quarkus:
  # Use test profile
  test:
    profile: test

  # HTTP configuration for tests
  http:
    port: 8081
    test-port: 8081

  # Database configuration for tests
  datasource:
    db-kind: postgresql
    jdbc: false
    reactive:
      max-size: 5
      initial-size: 2
      idle-timeout: PT5M
      max-lifetime: PT15M
      # Use test database or in-memory database
      url: ${DB_AUTH_URL:vertx-reactive:postgresql://localhost:35432/auth_test_db}
    username: ${DB_AUTH_USERNAME:auth}
    password: ${DB_AUTH_PASSWORD:z8swrUXftp3Zs9kBB5^sjH}

  # Hibernate configuration for tests
  hibernate-orm:
    database:
      generation: drop-and-create
    log:
      sql: false

  # Redis configuration for tests
  redis:
    hosts: ${REDIS_URL:redis://localhost:6379?password=123456}
    timeout: 5s
    max-pool-size: 5
    max-pool-waiting: 10

# Application configuration for tests
application:
  keycloakServerUri: https://identity-stage.glideyoke.com
  defaultTenantId: master
  caching:
    enabled: false  # Disable caching in tests for predictable behavior
  triggerVerifyEmail: false
  triggerNotifyEmail: false

  # Security configuration
  security:
    protected-usernames:
      - admin
      - superadmin
    protected-roles:
      - SUPER_ADMIN
      - SYSTEM_ADMIN

# Logging configuration for tests
quarkus.log:
  level: INFO
  category:
    "com.tripudiotech.authservice":
      level: DEBUG
    "io.restassured":
      level: DEBUG
  console:
    enable: true
    format: "%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n"

# REST Assured configuration
# Note: Don't set global REST Assured config here as it interferes with external API calls
