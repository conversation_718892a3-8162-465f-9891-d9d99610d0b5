/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.constant.SearchingParameterConstants;
import com.tripudiotech.authservice.request.GroupRequest;
import com.tripudiotech.authservice.response.UserGroupResponse;
import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.util.TokenUtils;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.db.query.ConditionKeyword;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.dto.UserGroupInformation;
import com.tripudiotech.securitylib.dto.UserRealmInformationResponse;
import com.tripudiotech.securitylib.dto.request.UserGroupRequest;
import com.tripudiotech.securitylib.dto.response.ErrorResponseDTO;
import com.tripudiotech.securitylib.service.provider.SecurityProviderService;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.ws.rs.core.GenericType;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.json.JSONObject;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.MultivaluedHashMap;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.tripudiotech.authservice.constant.EntityTypeConstants.PERSON_ENTITY_TYPE;
import static com.tripudiotech.datalib.db.DBConstants.AUTH_ID_PROPERTY;
import static com.tripudiotech.datalib.db.DBConstants.USER_GROUP_NODE_LABEL;

/**
 * <AUTHOR>
 */
@Slf4j
@ApplicationScoped
public class UserGroupService {
    private static final String LOG_PREFIX = "[UserGroupService]";
    @Inject
    @RestClient
    EntityServiceClient entityRepository;
    @Inject
    JsonWebToken jsonWebToken;
    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;
    @Inject
    UserService userService;


    public Uni<UserGroupResponse> create(@NonNull String tenantId, @NonNull GroupRequest request) {
        validateGroupRequest(tenantId, request);
        String groupName = request.getProperties().get(DBConstants.NAME_PROPERTY).toString();

        return createGroupInAuthServer(tenantId, groupName, request)
                .flatMap(result -> handleGroupCreationResult(tenantId, groupName, request, result));
    }

    private void validateGroupRequest(String tenantId, GroupRequest request) {
        if (request.getProperties().isEmpty() || !request.getProperties().containsKey(DBConstants.NAME_PROPERTY)) {
            throw new BadRequestException(tenantId, "Missing name property in group request");
        }
    }

    private Uni<GroupCreationResult> createGroupInAuthServer(String tenantId, String groupName, GroupRequest request) {
        return Uni.createFrom().item(() -> {
            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();

            try (Response response = securityProviderService.createUserGroup(tenantId, buildUserGroupRequest(groupName, request))) {
                return processAuthServerResponse(tenantId, groupName, response, securityProviderService);
            }
        }).runSubscriptionOn(Infrastructure.getDefaultWorkerPool());
    }

    private UserGroupRequest buildUserGroupRequest(String groupName, GroupRequest request) {
        return UserGroupRequest.builder()
                .groupType("Group")
                .name(groupName)
                .description(Optional.ofNullable(request.getProperties().get(DBConstants.DESCRIPTION_PROPERTY))
                        .map(Objects::toString)
                        .orElse(null))
                .build();
    }

    private GroupCreationResult processAuthServerResponse(String tenantId, String groupName, Response response, SecurityProviderService securityProviderService) {
        if (!Response.Status.Family.familyOf(response.getStatus()).equals(Response.Status.Family.SUCCESSFUL)) {
            response.bufferEntity();

            if (response.getStatus() == Response.Status.CONFLICT.getStatusCode()) {
                return handleExistingGroup(tenantId, groupName, securityProviderService);
            }

            throw createAuthServerException(tenantId, response);
        }

        return handleNewGroupCreation(tenantId, response);
    }

    private GroupCreationResult handleExistingGroup(String tenantId, String groupName, SecurityProviderService securityProviderService) {
        String authServerGeneratedId = securityProviderService
                .searchUserGroup(tenantId, groupName, 0, 1).get(0).getId();
        return new GroupCreationResult(TokenUtils.addBearerPrefix(jsonWebToken.getRawToken()), authServerGeneratedId, true);
    }

    private ServiceException createAuthServerException(String tenantId, Response response) {
        log.error("{} Failed to create user group in auth server. Response Status: {}", LOG_PREFIX, response.getStatus());
        String errorMessage = Optional.ofNullable(response.readEntity(ErrorResponseDTO.class).getErrorMessage())
                .map(Object::toString)
                .orElseGet(() -> response.readEntity(String.class));
        return new ServiceException(tenantId, BusinessErrorCode.fromHttpStatusCode(response.getStatus()), errorMessage);
    }

    private GroupCreationResult handleNewGroupCreation(String tenantId, Response response) {
        List<String> locationSplits = Optional.ofNullable(response.getHeaderString("location"))
                .map(location -> location.split("/"))
                .map(Arrays::asList)
                .orElse(Collections.emptyList());

        if (locationSplits.isEmpty()) {
            log.error("{} Unable to get user group ID from auth server response", LOG_PREFIX);
            throw new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR, "Failed to create user group");
        }

        String authServerGeneratedId = locationSplits.get(locationSplits.size() - 1);
        return new GroupCreationResult(TokenUtils.addBearerPrefix(jsonWebToken.getRawToken()), authServerGeneratedId, false);
    }

    private Uni<UserGroupResponse> handleGroupCreationResult(String tenantId, String groupName, GroupRequest request, GroupCreationResult result) {
        if (result.isExisting) {
            return handleExistingGroupInDatabase(tenantId, groupName, result.authId, request);
        }
        return createGroupInDatabase(result.token, tenantId, request, result.authId);
    }

    private Uni<UserGroupResponse> handleExistingGroupInDatabase(String tenantId, String groupName, String authId, GroupRequest request) {
        return findGroupByName(tenantId, groupName)
                .onItem().transform(entity -> new UserGroupResponse(authId, entity))
                .onFailure().recoverWithUni(throwable ->
                    createGroupInDatabase(TokenUtils.addBearerPrefix(jsonWebToken.getRawToken()), tenantId, request, authId));
    }

    private Uni<UserGroupResponse> createGroupInDatabase(String token, String tenantId, GroupRequest request, String authServerGeneratedId) {
        Map<String, Object> attributes = new HashMap<>(request.getProperties());
        attributes.put(AUTH_ID_PROPERTY, authServerGeneratedId);

        return entityRepository.createEntity(
                        token,
                        tenantId,
                        USER_GROUP_NODE_LABEL,
                        CreateEntityRequest.builder()
                                .relations(new HashSet<>())
                                .attributes(attributes)
                                .build())
                .map(response -> response.readEntity(EntityWithPermission.class))
                .map(createdEntity -> new UserGroupResponse(authServerGeneratedId, createdEntity))
                .onFailure().invoke(exception -> {
                    log.error("Rolling back data, deleting new created group in keycloak. Name: {}",
                            request.getProperties().get(DBConstants.NAME_PROPERTY).toString());
                    Optional.ofNullable(authServerGeneratedId).filter(StringUtils::isNoneBlank)
                            .ifPresent(generatedId -> {
                                try {
                                    securityProviderServiceFactory.getDefaultAuthenticateService().removeUserGroup(tenantId, generatedId);
                                } catch (Exception rollbackException) {
                                    log.error("Failed to rollback user group in keycloak. GroupId: {}, Error: {}",
                                            generatedId, rollbackException.getMessage(), rollbackException);
                                }
                            });
                });
    }


    public Uni<EntityWithPermission> update(String tenantId, String entityId, GroupRequest request) {
        if (request.getProperties() == null || request.getProperties().isEmpty()) {
            throw new BadRequestException(tenantId, "Group properties cannot be empty");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP_NODE_LABEL, entityId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(entity -> {
                    String authId = Optional.ofNullable(entity.getProperties().get(AUTH_ID_PROPERTY))
                            .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                                log.error("{} Could not find auth ID property for group ID: {}", LOG_PREFIX, entityId);
                                return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR,
                                    String.format("Auth ID not found for group: %s", entityId));
                            });

                    return Uni.createFrom().item(() -> {
                                SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                UserGroupInformation existingUserGroup = securityProviderService.getUserGroupById(tenantId, authId);
                                return Tuple2.of(entity, existingUserGroup);
                            })
                            .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                            .flatMap(tuple -> {
                                EntityWithPermission originalEntity = tuple.getItem1();
                                UserGroupInformation existingUserGroup = tuple.getItem2();

                                return entityRepository.updateEntity(token, tenantId, entityId,
                                                UpdateEntityRequest.builder()
                                                        .attributes(request.getProperties())
                                                        .build())
                                        .map(response -> response.readEntity(EntityWithPermission.class))
                                        .flatMap(updatedEntity -> {
                                            // Do update group name in keycloak
                                            return Uni.createFrom().item(() -> {
                                                        String newGroupName = Optional.of(request.getProperties())
                                                                .map(value -> value.get(DBConstants.NAME_PROPERTY))
                                                                .map(Object::toString).filter(StringUtils::isNoneBlank).orElse(null);
                                                        if (!StringUtils.isBlank(newGroupName) &&
                                                            !newGroupName.equals(existingUserGroup.getName())) {
                                                            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                                            securityProviderService.updateUserGroup(tenantId, authId, newGroupName);
                                                        }
                                                        return updatedEntity;
                                                    })
                                                    .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                                                    .onFailure().recoverWithUni(e -> {
                                                        log.error("{} Failed to updated group name in keycloak, rolling back the data in neo4j", LOG_PREFIX, e);
                                                        Map<String, Object> originalProperties = originalEntity.getProperties();
                                                        return entityRepository.updateEntity(token, tenantId, entityId,
                                                                        UpdateEntityRequest.builder()
                                                                                .attributes(originalProperties)
                                                                                .build())
                                                                .map(response -> response.readEntity(EntityWithPermission.class))
                                                                .onItem().failWith(() -> e);
                                                    });
                                        });
                            });
                });
    }

    public Uni<PageResponse<EntityWithPermission>> searchGroups(
            @NonNull String tenantId,
            String query,
            @NonNull Integer offset,
            @NonNull Integer limit
    ) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        return entityRepository.getAll(token, tenantId, USER_GROUP_NODE_LABEL, offset, limit, query, null, null, null, null)
                .map(response ->
                        response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {
                        }));
    }


    public Uni<Void> removeGroup(
            @NonNull String tenantId,
            @NonNull String groupId) {

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP_NODE_LABEL, groupId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(teamEntityWithPermission -> {
                    String authId = Optional.ofNullable(teamEntityWithPermission.getProperties().get(AUTH_ID_PROPERTY))
                            .map(Object::toString).orElse(null);

                    return entityRepository.deleteEntity(token, tenantId, groupId)
                            .replaceWithVoid()
                            .onItem().transformToUni(ignored -> {
                                if (!StringUtils.isBlank(authId)) {
                                    return Uni.createFrom().item(() -> {
                                        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                        securityProviderService.removeUserGroup(tenantId, authId);
                                        return (Void) null;
                                    }).runSubscriptionOn(Infrastructure.getDefaultWorkerPool());
                                } else {
                                    return Uni.createFrom().voidItem();
                                }
                            });
                });
    }


    public Uni<Void> addUserToGroup(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull String userEmail) {
        validateNonBlankParameter(tenantId, userEmail, "User email");
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP_NODE_LABEL, groupId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(entity -> {
                    String authId = Optional.ofNullable(entity.getProperties().get(AUTH_ID_PROPERTY))
                            .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                                log.error("{} Could not find auth ID property for group ID: {}", LOG_PREFIX, groupId);
                                return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR,
                                    String.format("Auth ID not found for group: %s", groupId));
                            });

                    return userService.getUserEntityByEmail(tenantId, userEmail)
                            .flatMap(addedUser -> Uni.createFrom().item(() -> {
                                SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                MultivaluedMap<String, String> queryParams = new MultivaluedHashMap<>();
                                queryParams.put(SearchingParameterConstants.EMAIL, List.of(userEmail.trim()));
                                List<UserRealmInformationResponse> userResponseList = securityProviderService.searchUsers(tenantId, queryParams);
                                if (userResponseList.isEmpty()) {
                                    throw new RecordNotFoundException(tenantId, String.format("User not found with email: %s", userEmail));
                                }
                                UserRealmInformationResponse userRealmInformationResponse = userResponseList.stream()
                                        .filter(data -> data.getEmail().equals(userEmail))
                                        .findFirst()
                                        .orElseThrow(() -> new RecordNotFoundException(tenantId, String.format("User not found with email: %s", userEmail)));

                                securityProviderService.addUserToGroup(tenantId, authId, userRealmInformationResponse.getId());

                                return null;
                            }).runSubscriptionOn(Infrastructure.getDefaultWorkerPool()).replaceWithVoid());
                })
                .replaceWithVoid();
    }

    /**
     * Adds a user to a group using pre-resolved auth server IDs for optimal performance.
     * This method bypasses user and group lookups by accepting both auth server user ID
     * and auth server group ID directly.
     *
     * @param tenantId the tenant identifier
     * @param groupId the internal group ID (for logging purposes)
     * @param authGroupId the auth server group ID
     * @param authServerUserId the auth server user ID
     * @return a Uni that completes when the user is successfully added to the group
     */
    public Uni<Void> addUserToAuthGroup(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull String authGroupId,
            @NonNull String authServerUserId) {
        validateNonBlankParameter(tenantId, authServerUserId, "Auth server user ID");
        validateNonBlankParameter(tenantId, authGroupId, "Auth group ID");

        return Uni.createFrom().item(() -> {
            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
            securityProviderService.addUserToGroup(tenantId, authGroupId, authServerUserId);

            return (Void) null;
        }).runSubscriptionOn(Infrastructure.getDefaultWorkerPool());
    }

    public Uni<Void> removeUserFromGroup(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull String userEmail) {
        validateNonBlankParameter(tenantId, userEmail, "User email");
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP_NODE_LABEL, groupId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(entity -> {
                    String authId = Optional.ofNullable(entity.getProperties().get(AUTH_ID_PROPERTY))
                            .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                                log.error("{} Could not find auth server ID property for group ID: {}", LOG_PREFIX, groupId);
                                return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR,
                                    String.format("Auth server ID not found for group: %s", groupId));
                            });

                    return userService.getUserEntityByEmail(tenantId, userEmail)
                            .flatMap(userInfo -> Uni.createFrom().item(() -> {
                                        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                        MultivaluedMap<String, String> queryParams = new MultivaluedHashMap<>();
                                        queryParams.put(SearchingParameterConstants.EMAIL, List.of(userEmail.trim()));
                                        List<UserRealmInformationResponse> userResponseList = securityProviderService.searchUsers(tenantId, queryParams);
                                        if (userResponseList.isEmpty()) {
                                            throw new RecordNotFoundException(tenantId, String.format("User not found with email: %s", userEmail));
                                        }

                                        UserRealmInformationResponse userRealmInformationResponse = userResponseList.stream()
                                                .filter(data -> data.getEmail().equals(userEmail))
                                                .findFirst()
                                                .orElseThrow(() -> new RecordNotFoundException(tenantId, String.format("User not found with email: %s", userEmail)));

                                        securityProviderService.removeUserToTheGroup(tenantId, authId, userRealmInformationResponse.getId());

                                        return userRealmInformationResponse;
                                    })
                                    .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                                    .flatMap(userRealmInformationResponse -> entityRepository.deleteRelation(
                                                    token, tenantId, userInfo.getId(), DBConstants.RELATION_WORKS_FOR, groupId)
                                            .replaceWithVoid()
                                            .onFailure().invoke(exception -> {
                                                log.error("{} Failed to delete relation between group and user. GroupId: {}, UserId: {}", LOG_PREFIX,
                                                        groupId, userInfo.getId(), exception);
                                                try {
                                                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                                    securityProviderService.addUserToGroup(tenantId, authId, userRealmInformationResponse.getId());
                                                } catch (Exception rollbackException) {
                                                    log.error("{} Failed to rollback user to group in keycloak. GroupId: {}, UserId: {}", LOG_PREFIX,
                                                            groupId, userInfo.getId(), rollbackException);
                                                }
                                            })
                                            .onFailure().transform(exception ->
                                                    new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR))));
                });
    }


    public Uni<PageResponse<?>> getUsersInGroupBy(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull Integer offset,
            @NonNull Integer limit
    ) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        return entityRepository.getEntitiesUnderRelationType(
                token,
                tenantId,
                offset,
                limit,
                groupId,
                DBConstants.RELATION_WORKS_FOR,
                PERSON_ENTITY_TYPE,
                true
        ).map(response -> response.readEntity(PageResponse.class));
    }

    /**
     * Finds an existing group by name or creates a new one if it doesn't exist.
     */
    public Uni<EntityWithPermission> findOrCreateGroupByName(@NonNull String tenantId, @NonNull String groupName) {
        if (StringUtils.isBlank(groupName)) {
            throw new BadRequestException(tenantId, "Group name cannot be empty or blank");
        }
        // Search for existing group by name
        JSONObject jsonQuery = new JSONObject()
                .put(
                        ConditionKeyword.EXACT.getValue(),
                        new JSONObject()
                                .put(String.format("%s", DBConstants.NAME_PROPERTY), groupName));

        return searchGroups(tenantId, jsonQuery.toString(), 0, 1).map(pageResponse -> {

            if (pageResponse.getData() != null && !pageResponse.getData().isEmpty()) {
                EntityWithPermission entityWithPermission = pageResponse.getData().get(0);

                if (entityWithPermission == null || StringUtils.isBlank(entityWithPermission.getId())) {
                    throw new RecordNotFoundException(tenantId, "Group not found with name: " + groupName);
                }

                // Check if authId is present - if not, this group is not properly linked
                if (entityWithPermission.getProperties().get(AUTH_ID_PROPERTY) == null) {
                    log.warn("{} Group found but missing authId property, treating as not found: {}", LOG_PREFIX, groupName);
                    throw new RecordNotFoundException(tenantId, "Group not found with name: " + groupName);
                }

                log.debug("{} Found group with name: {} and groupId: {} authId: {}", LOG_PREFIX, groupName, entityWithPermission.getId(),
                        entityWithPermission.getProperties().get(AUTH_ID_PROPERTY));

                return entityWithPermission;
            }
            return null;
        }).onItem().ifNull().switchTo(() -> {
                    log.info("{} Group '{}' not found, creating new group", LOG_PREFIX, groupName);

                    Map<String, Object> groupProperties = new HashMap<>();
                    groupProperties.put(DBConstants.NAME_PROPERTY, groupName);
                    groupProperties.put(DBConstants.DESCRIPTION_PROPERTY, "Auto-created group for user assignment");

                    GroupRequest groupRequest = GroupRequest.builder()
                            .properties(groupProperties)
                            .build();

                    return create(tenantId, groupRequest)
                            .onItem().transform(createdGroupResponse -> {
                                log.info("{} Created new group with name: {} and groupId: {} authId: {}", LOG_PREFIX, groupName, createdGroupResponse.getId(), createdGroupResponse.getAuthId());
                                // Convert UserGroupResponse back to EntityWithPermission
                                EntityWithPermission entity = new EntityWithPermission();
                                entity.setId(createdGroupResponse.getId());
                                entity.setCreatedAt(createdGroupResponse.getCreatedAt());
                                entity.setUpdatedAt(createdGroupResponse.getUpdatedAt());
                                entity.setPermissions(createdGroupResponse.getPermissions());
                                entity.setProperties(createdGroupResponse.getProperties());
                                entity.setState(createdGroupResponse.getState());
                                entity.setOwner(createdGroupResponse.getOwner());
                                entity.setCreatedBy(createdGroupResponse.getCreatedBy());
                                entity.setDisabled(createdGroupResponse.isDisabled());
                                return entity;
                            });
                }
        );
    }

    /**
     * Get a group by ID.
     */
    public Uni<EntityWithPermission> getById(@NonNull String tenantId, @NonNull String groupId) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP_NODE_LABEL, groupId)
                .map(response -> {
                    EntityWithPermission entityWithPermission = response.readEntity(EntityWithPermission.class);

                    if (entityWithPermission == null || StringUtils.isBlank(entityWithPermission.getId())) {
                        throw new RecordNotFoundException(tenantId, "Group not found with ID: " + groupId);
                    }

                    return entityWithPermission;
                });

    }

    /**
     * Find a group by name (without auto-creation).
     */
    public Uni<EntityWithPermission> findGroupByName(@NonNull String tenantId, @NonNull String groupName) {
        validateNonBlankParameter(tenantId, groupName, "Group name");
        return findGroupByProperty(tenantId, DBConstants.NAME_PROPERTY, groupName, "name");
    }

    /**
     * Find a group by external ID.
     */
    public Uni<EntityWithPermission> findGroupByExternalId(@NonNull String tenantId, @NonNull String externalId) {
        validateNonBlankParameter(tenantId, externalId, "External ID");
        return findGroupByProperty(tenantId, AUTH_ID_PROPERTY, externalId, "external ID");
    }

    /**
     * Find a group by code.
     */
    public Uni<EntityWithPermission> findGroupByCode(@NonNull String tenantId, @NonNull String code) {
        validateNonBlankParameter(tenantId, code, "Group code");
        return findGroupByProperty(tenantId, "code", code, "code");
    }

    /**
     * Common method to find a group by any property.
     */
    private Uni<EntityWithPermission> findGroupByProperty(String tenantId, String propertyName, String propertyValue, String displayName) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        String query = new JSONObject().put("$exact", new JSONObject().put(propertyName, propertyValue)).toString();

        return entityRepository.getAll(token, tenantId, USER_GROUP_NODE_LABEL, 0, 1, query, null, null, null, null)
                .map(response -> response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {}))
                .map(pageResponse -> {
                    if (pageResponse.getData() != null && !pageResponse.getData().isEmpty()) {
                        EntityWithPermission entity = pageResponse.getData().get(0);
                        if (entity != null && StringUtils.isNotBlank(entity.getId())) {
                            return entity;
                        }
                    }
                    throw new RecordNotFoundException(tenantId, String.format("Group not found with %s: %s", displayName, propertyValue));
                });
    }

    /**
     * Validates that a parameter is not blank.
     */
    private void validateNonBlankParameter(String tenantId, String value, String parameterName) {
        if (StringUtils.isBlank(value)) {
            throw new BadRequestException(tenantId, String.format("%s cannot be empty or blank", parameterName));
        }
    }

    /**
     * Result of group creation in auth server.
     */
    private record GroupCreationResult(String token, String authId, boolean isExisting) {
    }
}
