/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.integration.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;

/**
 * Helper class for managing test data in integration tests.
 * Provides methods to create, retrieve, and clean up test entities.
 */
@ApplicationScoped
@Slf4j
public class TestDataHelper {

    @Inject
    ObjectMapper objectMapper;

    @Inject
    AuthenticationHelper authHelper;

    @ConfigProperty(name = "test.company.id", defaultValue = "test-company-id")
    String testCompanyId;

    /**
     * Generates test data for user creation.
     *
     * @return UserTestData with randomized values
     */
    public UserTestData generateUserTestData() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH.mm"));
        String randomId = UUID.randomUUID().toString().substring(0, 8);

        String email = String.format("<EMAIL>", randomId);
        String username = email;
        String firstName = "Test";
        String lastName = "User" + randomId;

        return UserTestData.builder()
                .username(username)
                .email(email)
                .firstName(firstName)
                .lastName(lastName)
                .description("This is test user created for integration test")
                .groupNames(new String[]{
                        timestamp + " Group 1",
                        timestamp + " Group 2",
                        timestamp + " Group 3"
                })
                .build();
    }

    /**
     * Gets or creates a test company for testing.
     *
     * @return the company ID
     */
    public String getOrCreateTestCompany() {
        // For now, return the configured test company ID
        // In a real implementation, you might want to create a test company
        return testCompanyId;
    }

    /**
     * Gets the auth user ID by email.
     * This is a placeholder - implement based on your auth service capabilities.
     *
     * @param email the user email
     * @return the auth user ID
     */
    public String getAuthUserIdByEmail(String email) {
        try {
            // This would typically call your auth service or Keycloak API
            // For now, return a placeholder
            log.info("Getting auth user ID for email: {}", email);

            // TODO: Implement actual auth user lookup
            // This might involve calling Keycloak admin API or your user service
            return "auth-user-" + email.hashCode();

        } catch (Exception e) {
            log.error("Failed to get auth user ID for email: {}", email, e);
            return null;
        }
    }

    /**
     * Gets or creates a group by name.
     *
     * @param groupName the group name
     * @return the group ID
     */
    public String getOrCreateGroupByName(String groupName) {
        try {
            String accessToken = authHelper.getAccessToken();

            // First, try to find existing group
            // TODO: Implement group search API call

            // If not found, create new group
            Map<String, Object> groupProperties = new HashMap<>();
            groupProperties.put("name", groupName);
            groupProperties.put("description", "Test group created for integration test");

            Map<String, Object> groupRequest = new HashMap<>();
            groupRequest.put("properties", groupProperties);

            Response response = given()
                    .header("Authorization", "Bearer " + accessToken)
                    .header("X-Tenant-Id", "master")
                    .contentType(ContentType.JSON)
                    .body(groupRequest)
                    .when()
                    .post("/group")
                    .then()
                    .statusCode(200)
                    .extract()
                    .response();

            JsonNode responseJson = objectMapper.readTree(response.asString());
            String groupId = responseJson.get("id").asText();

            log.info("Created/found group: {} with ID: {}", groupName, groupId);
            return groupId;

        } catch (Exception e) {
            log.error("Failed to get or create group: {}", groupName, e);
            return null;
        }
    }

    /**
     * Checks if a user is assigned to a group.
     *
     * @param userId the user ID
     * @param groupName the group name
     * @return true if assigned, false otherwise
     */
    public boolean isUserAssignedToGroup(String userId, String groupName) {
        try {
            // TODO: Implement group membership check
            // This would typically call your user service to check group assignments
            log.info("Checking if user {} is assigned to group {}", userId, groupName);

            // For now, assume assignment was successful
            // In a real implementation, you would call an API to verify the assignment
            return true;

        } catch (Exception e) {
            log.error("Failed to check group assignment for user {} and group {}", userId, groupName, e);
            return false;
        }
    }

    /**
     * Deletes a user.
     *
     * @param userId the user ID
     */
    public void deleteUser(String userId) {
        try {
            String accessToken = authHelper.getAccessToken();

            given()
                    .header("Authorization", "Bearer " + accessToken)
                    .header("X-Tenant-Id", "master")
                    .when()
                    .delete("/user/{userId}", userId)
                    .then()
                    .statusCode(anyOf(equalTo(200), equalTo(204), equalTo(404))); // 404 is OK if already deleted

            log.info("Deleted user: {}", userId);

        } catch (Exception e) {
            log.error("Failed to delete user: {}", userId, e);
        }
    }

    /**
     * Deletes an auth user.
     *
     * @param authUserId the auth user ID
     */
    public void deleteAuthUser(String authUserId) {
        try {
            // TODO: Implement auth user deletion
            // This would typically call Keycloak admin API
            log.info("Deleting auth user: {}", authUserId);

        } catch (Exception e) {
            log.error("Failed to delete auth user: {}", authUserId, e);
        }
    }

    /**
     * Deletes a group.
     *
     * @param groupId the group ID
     */
    public void deleteGroup(String groupId) {
        try {
            String accessToken = authHelper.getAccessToken();

            given()
                    .header("Authorization", "Bearer " + accessToken)
                    .header("X-Tenant-Id", "master")
                    .when()
                    .delete("/group/{groupId}", groupId)
                    .then()
                    .statusCode(anyOf(equalTo(200), equalTo(204), equalTo(404))); // 404 is OK if already deleted

            log.info("Deleted group: {}", groupId);

        } catch (Exception e) {
            log.error("Failed to delete group: {}", groupId, e);
        }
    }
}
